<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view class="card-layout-wrapper">
		<!-- 外部白色背景容器 -->
		<view class="page-wrapper bg-white">
			<!-- 内部带颜色卡片 -->
			<view class="page-content" :style="{ backgroundColor: cardBgColor }">
				<!-- 顶部圆形图标 -->
				<view class="top-icon">
					<view class="circle-icon" :style="{ backgroundColor: iconColor }"></view>
				</view>
				
				<!-- 内容插槽 -->
				<slot name="content"></slot>
			</view>
		</view>
	</view>
</template>

<script>
/**
 * 卡片布局组件
 * 提供外部白色背景+内部带颜色卡片+顶部圆形图标的通用布局
 */
export default {
	name: 'CardLayout',
	props: {
		// 圆形图标颜色
		iconColor: {
			type: String,
			default: '#81958f'
		},
		// 卡片背景颜色
		cardBgColor: {
			type: String,
			default: '#eaeaea'
		}
	}
}
</script>

<style scoped>
/* 页面整体布局 */
.page-wrapper {
	min-height: 100vh;
	padding: 20rpx;
	padding-top: 50rpx;
}

/* 页面内容卡片 */
.page-content {
	position: relative; /* 为顶部圆球定位提供参考 */
	border-radius: 10rpx;
	min-height: calc(100vh - 40rpx);
	padding-top: 65rpx; /* 为顶部圆形图标留出空间 */
}

/* 顶部圆形图标 */
.top-icon {
	position: absolute;
	top: -40rpx; /* 浮动在卡片顶部中间位置 */
	left: 50%;
	transform: translateX(-50%);
	z-index: 10;
}

.circle-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	/* 背景颜色通过内联样式设置 */
}
</style>
